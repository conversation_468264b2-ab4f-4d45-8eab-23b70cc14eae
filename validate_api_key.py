#!/usr/bin/env python3
"""
验证API密钥格式和基本信息
"""
import os
import re
from dotenv import load_dotenv

def validate_api_key():
    # 加载环境变量
    load_dotenv(override=True)
    
    api_key = os.getenv("DASHSCOPE_API_KEY")
    
    print("🔍 API密钥验证")
    print("=" * 50)
    
    if not api_key:
        print("❌ API密钥未设置")
        return False
    
    print(f"API密钥: {api_key}")
    print(f"长度: {len(api_key)} 字符")
    
    # 检查格式
    if api_key.startswith("sk-"):
        print("✅ 格式正确：以 'sk-' 开头")
    else:
        print("❌ 格式错误：应该以 'sk-' 开头")
        return False
    
    # 检查长度（阿里云API密钥通常是特定长度）
    if len(api_key) >= 30:
        print("✅ 长度合理")
    else:
        print("❌ 长度可能不正确")
        return False
    
    # 检查字符组成
    if re.match(r'^sk-[a-zA-Z0-9]+$', api_key):
        print("✅ 字符组成正确：只包含字母和数字")
    else:
        print("❌ 字符组成错误：包含非法字符")
        return False
    
    print("\n📋 建议检查项目：")
    print("1. 登录阿里云百炼控制台验证API密钥状态")
    print("2. 确认API密钥是否有访问qwen-max模型的权限")
    print("3. 检查API密钥是否已过期")
    print("4. 如有问题，重新生成新的API密钥")
    
    return True

if __name__ == "__main__":
    validate_api_key()
