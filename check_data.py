import pandas as pd

# 读取生成的财务数据
df = pd.read_csv('financial_data.csv')

print("=== 财务数据概览 ===")
print(f"总行数: {len(df)}")
print(f"公司数量: {df['公司'].nunique()}")
print(f"年份范围: {df['年份'].min()}-{df['年份'].max()}")
print(f"月份范围: {df['月份'].min()}-{df['月份'].max()}")
print(f"财报类型: {list(df['财报类型'].unique())}")
print(f"数据类型: {list(df['数据类型'].unique())}")

print("\n=== 各公司名称 ===")
for i, company in enumerate(df['公司'].unique(), 1):
    print(f"{i}. {company}")

print("\n=== 资产负债表项目 ===")
balance_items = df[df['财报类型'] == '资产负债表']['财报项目'].unique()
for i, item in enumerate(balance_items, 1):
    print(f"{i}. {item}")

print("\n=== 利润表项目 ===")
profit_items = df[df['财报类型'] == '利润表']['财报项目'].unique()
for i, item in enumerate(profit_items, 1):
    print(f"{i}. {item}")

print("\n=== 数据样例（前10行）===")
print(df.head(10))

print("\n=== 数据统计信息 ===")
print(f"数据值范围: {df['数据'].min():.2f} 到 {df['数据'].max():.2f}")
print(f"数据平均值: {df['数据'].mean():.2f}")
