#!/usr/bin/env python3
"""
直接测试API密钥是否有效
"""
import os
from dotenv import load_dotenv
from openai import OpenAI

def test_api_key_direct():
    # 加载环境变量
    load_dotenv(override=True)
    
    api_key = os.getenv("DASHSCOPE_API_KEY")
    base_url = os.getenv("OPENAI_API_BASE")
    model = os.getenv("LLM_MODEL", "qwen-max")
    
    print(f"测试配置:")
    print(f"  API密钥: {api_key[:10] if api_key else 'None'}...")
    print(f"  Base URL: {base_url}")
    print(f"  模型: {model}")
    print()
    
    if not api_key:
        print("❌ API密钥未设置")
        return False
    
    try:
        # 创建OpenAI客户端
        client = OpenAI(
            api_key=api_key,
            base_url=base_url
        )
        
        print("正在发送测试请求...")
        
        # 尝试不同的模型
        models_to_try = [model, "qwen-plus", "qwen-turbo", "gpt-3.5-turbo"]

        for test_model in models_to_try:
            print(f"尝试模型: {test_model}")
            try:
                response = client.chat.completions.create(
                    model=test_model,
                    messages=[
                        {"role": "user", "content": "Hello, please respond with just 'OK'"}
                    ],
                    max_tokens=5,
                    timeout=30
                )
                print(f"✅ 模型 {test_model} 调用成功！")
                print(f"响应: {response.choices[0].message.content}")
                return True
            except Exception as model_error:
                print(f"❌ 模型 {test_model} 失败: {model_error}")
                continue

        print("所有模型都失败了")
        

        
    except Exception as e:
        print(f"❌ API调用失败: {e}")
        
        # 检查是否是401错误
        if "401" in str(e) or "Unauthorized" in str(e):
            print("\n🔍 401错误分析:")
            print("1. API密钥可能无效或已过期")
            print("2. API密钥可能没有访问qwen-max模型的权限")
            print("3. 阿里云百炼服务可能有问题")
            print("\n建议:")
            print("- 检查阿里云百炼控制台中的API密钥状态")
            print("- 确认API密钥有访问qwen-max模型的权限")
            print("- 尝试重新生成API密钥")
        
        return False

if __name__ == "__main__":
    test_api_key_direct()
