#!/usr/bin/env python3
"""
测试PandasAI LocalLLM配置
"""
import os
import pandas as pd
from dotenv import load_dotenv

def test_pandasai_localllm():
    # 加载环境变量
    load_dotenv()
    
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_API_BASE")
    model = os.getenv("LLM_MODEL", "qwen-max")
    
    print(f"API密钥: {api_key[:10] if api_key else 'None'}...")
    print(f"Base URL: {base_url}")
    print(f"模型: {model}")
    
    if not api_key:
        print("❌ API密钥未设置")
        return False
    
    try:
        # 设置环境变量
        os.environ['OPENAI_API_KEY'] = api_key
        
        # 导入PandasAI
        from pandasai import SmartDataframe
        from pandasai.llm.local_llm import LocalLLM
        
        print("正在创建LocalLLM实例...")
        
        # 创建LocalLLM实例
        llm = LocalLLM(
            api_base=base_url,
            model=model
        )
        
        print("✅ LocalLLM实例创建成功！")
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'name': ['Alice', 'Bob', 'Charlie'],
            'age': [25, 30, 35],
            'salary': [50000, 60000, 70000]
        })
        
        print("正在创建SmartDataframe...")
        
        # 创建SmartDataframe
        df = SmartDataframe(test_data, config={"llm": llm})
        
        print("✅ SmartDataframe创建成功！")
        
        # 测试简单查询
        print("正在测试简单查询...")
        
        try:
            result = df.chat("How many rows are in this data?")
            print(f"✅ 查询成功！结果: {result}")
            return True
        except Exception as query_error:
            print(f"⚠️ 查询失败，但配置可能正确: {query_error}")
            return True  # 配置正确，但查询可能因为网络等原因失败
            
    except Exception as e:
        print(f"❌ 配置失败: {e}")
        return False

if __name__ == "__main__":
    test_pandasai_localllm()
