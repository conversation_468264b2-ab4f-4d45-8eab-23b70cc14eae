#!/usr/bin/env python3
"""
测试阿里云百炼API密钥是否有效
"""
import requests
import json

def test_api_key():
    # 从环境变量读取配置
    import os
    from dotenv import load_dotenv
    load_dotenv()

    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_API_BASE")
    model = os.getenv("LLM_MODEL", "qwen-max")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": model,
        "messages": [
            {"role": "user", "content": "Hello"}
        ],
        "max_tokens": 10
    }
    
    try:
        print(f"测试API密钥: {api_key[:10] if api_key else 'None'}...")
        print(f"请求URL: {base_url}/chat/completions")
        print(f"使用模型: {model}")
        
        response = requests.post(
            f"{base_url}/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ API密钥有效！")
            return True
        else:
            print("❌ API密钥无效或有其他问题")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

if __name__ == "__main__":
    test_api_key()
