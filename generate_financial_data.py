import pandas as pd
import numpy as np
import datetime

def generate_full_financial_data():
    """
    生成2023.1-2025.12期间，10家虚拟建筑公司完整且逻辑正确的财务数据。
    """
    companies = [
        "中建基业集团", "华西路桥工程", "宏远天工建筑", "东方明珠建设",
        "巨龙隧道股份", "新纪元房产", "九州铁建", "四海基础工程",
        "安居置业集团", "天工路桥建设"
    ]
    
    start_date = datetime.date(2023, 1, 1)
    end_date = datetime.date(2025, 12, 31)
    date_range = pd.date_range(start_date, end_date, freq='M')

    # 资产负债表和利润表项目
    balance_sheet_items = [
        "货币资金", "应收账款", "存货", "合同资产", "固定资产", "在建工程", "无形资产",
        "短期借款", "应付账款", "合同负债", "长期借款",
        "实收资本", "资本公积", "盈余公积", "未分配利润"
    ]
    
    profit_loss_items = [
        "营业总收入", "营业成本", "税金及附加", "销售费用", "管理费用", "研发费用",
        "财务费用", "投资收益", "营业外收入", "营业外支出"
    ]

    all_data = []

    # 为每家公司生成基准数据
    for company in companies:
        # 初始化期初余额
        np.random.seed(sum(ord(c) for c in company)) # 保证每家公司数据不同但可复现
        
        # 资产
        cash = np.random.uniform(2e8, 5e8)
        accounts_receivable = np.random.uniform(1e8, 3e8)
        inventory = np.random.uniform(2e8, 4e8)
        contract_assets = np.random.uniform(1e8, 3e8)
        fixed_assets = np.random.uniform(5e8, 10e8)
        construction_in_progress = np.random.uniform(1e8, 4e8)
        intangible_assets = np.random.uniform(0.5e8, 1e8)

        # 负债
        short_term_loan = np.random.uniform(2e8, 4e8)
        accounts_payable = np.random.uniform(2e8, 4e8)
        contract_liabilities = np.random.uniform(1e8, 3e8)
        long_term_loan = np.random.uniform(4e8, 8e8)

        # 权益
        paid_in_capital = np.random.uniform(5e8, 10e8)
        capital_reserve = np.random.uniform(1e8, 2e8)
        surplus_reserve = paid_in_capital * 0.1
        
        # 计算初始未分配利润以配平资产负债表
        total_assets = cash + accounts_receivable + inventory + contract_assets + fixed_assets + construction_in_progress + intangible_assets
        total_liabilities = short_term_loan + accounts_payable + contract_liabilities + long_term_loan
        equity_part = paid_in_capital + capital_reserve + surplus_reserve
        undistributed_profit = total_assets - total_liabilities - equity_part
        
        bs_initial = {
            "货币资金": cash, "应收账款": accounts_receivable, "存货": inventory,
            "合同资产": contract_assets, "固定资产": fixed_assets, "在建工程": construction_in_progress,
            "无形资产": intangible_assets, "短期借款": short_term_loan, "应付账款": accounts_payable,
            "合同负债": contract_liabilities, "长期借款": long_term_loan, "实收资本": paid_in_capital,
            "资本公积": capital_reserve, "盈余公积": surplus_reserve, "未分配利润": undistributed_profit
        }

        # 年度累计清零
        pl_yearly_cumulative = {item: 0 for item in profit_loss_items}
        pl_yearly_cumulative.update({"营业利润":0, "利润总额":0, "所得税费用":0, "净利润":0})


        for date in date_range:
            year = date.year
            month = date.month

            # 如果是新的一年，重置累计数
            if month == 1:
                pl_yearly_cumulative = {item: 0 for item in pl_yearly_cumulative}

            # ===================
            # 生成利润表 (本月数)
            # ===================
            pl_monthly = {}
            pl_monthly["营业总收入"] = np.random.uniform(0.8e8, 1.5e8) * (1 + np.sin(month / 12 * 2 * np.pi) * 0.2)
            pl_monthly["营业成本"] = pl_monthly["营业总收入"] * np.random.uniform(0.75, 0.85)
            pl_monthly["税金及附加"] = pl_monthly["营业总收入"] * np.random.uniform(0.005, 0.015)
            pl_monthly["销售费用"] = pl_monthly["营业总收入"] * np.random.uniform(0.02, 0.04)
            pl_monthly["管理费用"] = pl_monthly["营业总收入"] * np.random.uniform(0.04, 0.06)
            pl_monthly["研发费用"] = pl_monthly["营业总收入"] * np.random.uniform(0.03, 0.05)
            pl_monthly["财务费用"] = (bs_initial["短期借款"] + bs_initial["长期借款"]) * (0.05 / 12) # 模拟月度利息
            pl_monthly["投资收益"] = np.random.uniform(-0.01e7, 0.05e7)
            
            operating_profit = (pl_monthly["营业总收入"] - pl_monthly["营业成本"] - pl_monthly["税金及附加"] -
                                pl_monthly["销售费用"] - pl_monthly["管理费用"] - pl_monthly["研发费用"] -
                                pl_monthly["财务费用"] + pl_monthly["投资收益"])
            
            pl_monthly["营业外收入"] = np.random.uniform(0, 0.01e7)
            pl_monthly["营业外支出"] = np.random.uniform(0, 0.005e7)
            
            profit_before_tax = operating_profit + pl_monthly["营业外收入"] - pl_monthly["营业外支出"]
            income_tax = profit_before_tax * 0.25 if profit_before_tax > 0 else 0
            net_profit = profit_before_tax - income_tax

            # ===================
            # 更新资产负债表
            # ===================
            bs_ending = bs_initial.copy()
            
            # 核心联动：净利润转入未分配利润
            bs_ending["未分配利润"] += net_profit

            # 其他科目变动模拟
            bs_ending["货币资金"] += net_profit - np.random.uniform(0.5e7, 1e7) # 模拟现金流出
            bs_ending["应收账款"] += pl_monthly["营业总收入"] * np.random.uniform(0.1, 0.3)
            bs_ending["存货"] += pl_monthly["营业成本"] * np.random.uniform(0.1, 0.2)
            bs_ending["应付账款"] += pl_monthly["营业成本"] * np.random.uniform(0.1, 0.3)
            bs_ending["合同资产"] += pl_monthly["营业总收入"] * np.random.uniform(0.05, 0.1)
            bs_ending["合同负债"] += pl_monthly["营业总收入"] * np.random.uniform(0.05, 0.1)
            bs_ending["固定资产"] -= bs_initial["固定资产"] / (10 * 12) # 10年折旧
            bs_ending["无形资产"] -= bs_initial["无形资产"] / (5 * 12) # 5年摊销

            # 平衡轧差项：通常是短期借款或货币资金，此处用短期借款调整
            current_total_assets = sum(v for k, v in bs_ending.items() if k in ["货币资金", "应收账款", "存货", "合同资产", "固定资产", "在建工程", "无形资产"])
            current_total_liabilities_equity = sum(v for k, v in bs_ending.items() if k not in ["货币资金", "应收账款", "存货", "合同资产", "固定资产", "在建工程", "无形资产"])
            diff = current_total_assets - current_total_liabilities_equity
            bs_ending["短期借款"] += diff

            # ===================
            # 记录数据
            # ===================
            # 记录资产负债表
            for item in balance_sheet_items:
                all_data.append([company, year, month, '资产负债表', item, '期初余额', bs_initial[item]])
                all_data.append([company, year, month, '资产负债表', item, '期末余额', bs_ending[item]])
            
            # 记录利润表
            full_pl_monthly = {**pl_monthly, "营业利润": operating_profit, "利润总额": profit_before_tax, "所得税费用": income_tax, "净利润": net_profit}
            for item, value in full_pl_monthly.items():
                pl_yearly_cumulative[item] += value
                all_data.append([company, year, month, '利润表', item, '本月数', value])
                all_data.append([company, year, month, '利润表', item, '本年累计数', pl_yearly_cumulative[item]])
            
            # 为下个月做准备
            bs_initial = bs_ending

    # 创建DataFrame并保存
    df = pd.DataFrame(all_data, columns=['公司', '年份', '月份', '财报类型', '财报项目', '数据类型', '数据'])
    df['数据'] = df['数据'].round(2)
    df.to_csv('financial_data.csv', index=False, encoding='utf-8-sig')
    print("成功生成'financial_data.csv'文件！")
    print(f"数据包含：{len(companies)}家公司，{len(date_range)}个月份的财务数据")
    print(f"总计生成{len(df)}条记录")
    return df

if __name__ == '__main__':
    generate_full_financial_data()
