#!/usr/bin/env python3
"""
测试OpenAI客户端配置
"""
import os
from dotenv import load_dotenv
from openai import OpenAI

def test_openai_client():
    # 加载环境变量
    load_dotenv()
    
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_API_BASE")
    model = os.getenv("LLM_MODEL", "qwen-max")
    
    print(f"API密钥: {api_key[:10] if api_key else 'None'}...")
    print(f"Base URL: {base_url}")
    print(f"模型: {model}")
    
    if not api_key:
        print("❌ API密钥未设置")
        return False
    
    try:
        # 创建OpenAI客户端
        client = OpenAI(
            api_key=api_key,
            base_url=base_url
        )
        
        print("正在测试API连接...")
        
        # 发送测试请求
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "user", "content": "Hello"}
            ],
            max_tokens=10,
            timeout=10  # 设置较短的超时时间
        )
        
        print("✅ API连接成功！")
        print(f"响应: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False

if __name__ == "__main__":
    test_openai_client()
