#!/usr/bin/env python3
"""
测试OpenAI客户端配置 - 连接阿里云百炼平台Qwen API
"""
import os
import sys
from pathlib import Path
from dotenv import load_dotenv
from openai import OpenAI

def test_openai_client():
    """测试OpenAI客户端连接到阿里云百炼平台"""
    print("=" * 50)
    print("测试阿里云百炼平台 Qwen API 连接")
    print("=" * 50)

    # 确保从正确的路径加载环境变量
    env_path = Path(__file__).parent / '.env'
    print(f"环境变量文件路径: {env_path}")
    print(f"环境变量文件存在: {env_path.exists()}")

    # 加载环境变量
    load_dotenv(env_path)

    # 获取配置
    api_key = os.getenv("DASHSCOPE_API_KEY")
    base_url = os.getenv("OPENAI_API_BASE")
    model = os.getenv("LLM_MODEL", "qwen-max")

    print(f"\n配置信息:")
    print(f"API密钥: {api_key[:10] + '...' if api_key else 'None'}")
    print(f"Base URL: {base_url}")
    print(f"模型: {model}")

    # 验证必要的配置
    if not api_key:
        print("\n❌ 错误: DASHSCOPE_API_KEY 未设置")
        print("请检查 .env 文件中的 DASHSCOPE_API_KEY 配置")
        return False

    if not base_url:
        print("\n❌ 错误: OPENAI_API_BASE 未设置")
        print("请检查 .env 文件中的 OPENAI_API_BASE 配置")
        return False

    try:
        print(f"\n正在创建OpenAI客户端...")
        # 创建OpenAI客户端
        client = OpenAI(
            api_key=api_key,
            base_url=base_url
        )

        print("正在测试API连接...")

        # 发送测试请求
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "user", "content": "你好，请简单介绍一下你自己。"}
            ],
            max_tokens=100,
            temperature=0.1,
            timeout=30  # 增加超时时间
        )

        print("\n✅ API连接成功！")
        print(f"模型响应: {response.choices[0].message.content}")
        print(f"使用的模型: {response.model}")
        print(f"消耗的tokens: {response.usage.total_tokens if response.usage else 'N/A'}")

        return True

    except Exception as e:
        print(f"\n❌ API连接失败:")
        print(f"错误类型: {type(e).__name__}")
        print(f"错误信息: {str(e)}")

        # 提供更详细的错误诊断
        if "401" in str(e) or "Unauthorized" in str(e):
            print("\n🔍 诊断: API密钥可能无效或已过期")
            print("请检查 DASHSCOPE_API_KEY 是否正确")
        elif "404" in str(e) or "Not Found" in str(e):
            print("\n🔍 诊断: API端点或模型名称可能不正确")
            print("请检查 OPENAI_API_BASE 和 LLM_MODEL 配置")
        elif "timeout" in str(e).lower():
            print("\n🔍 诊断: 请求超时")
            print("请检查网络连接或增加超时时间")

        return False

def test_environment_variables():
    """测试环境变量加载"""
    print("\n" + "=" * 30)
    print("环境变量测试")
    print("=" * 30)

    env_path = Path(__file__).parent / '.env'
    load_dotenv(env_path)

    required_vars = ["DASHSCOPE_API_KEY", "OPENAI_API_BASE", "LLM_MODEL"]

    for var in required_vars:
        value = os.getenv(var)
        if value:
            display_value = value[:10] + "..." if var == "DASHSCOPE_API_KEY" else value
            print(f"✅ {var}: {display_value}")
        else:
            print(f"❌ {var}: 未设置")

if __name__ == "__main__":
    # 首先测试环境变量
    test_environment_variables()

    # 然后测试API连接
    success = test_openai_client()

    if success:
        print(f"\n🎉 所有测试通过！阿里云百炼平台连接正常。")
        sys.exit(0)
    else:
        print(f"\n💥 测试失败，请检查配置。")
        sys.exit(1)
